/* pages/store/index.wxss */

/* 店铺详情页样式 */
page {
	background-color: #f5f5f5;
}

.store-detail {
	width: 100%;
}

/* 顶部导航栏 */
.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	height: 188rpx;
	background-color: #ffde59;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	z-index: 100;
}

.nav-back {
	width: 48rpx;
	height: 48rpx;
	margin-top: 80rpx;
}

.nav-title {
	margin-top: 80rpx;
	font-size: 28rpx;
	/* font-weight: 500;
  color: #000000; */
}

.nav-actions {
	display: flex;
	align-items: center;
}

.circle-icon {
	width: 70rpx;
	height: 70rpx;
	border-radius: 50%;
	background-color: #333;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 24rpx;
}

/* 店铺信息卡片 */
.store-info {
	padding: 30rpx 30rpx;
}

.store-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.store-header {
	display: flex;
	align-items: flex-start;
}

.store-logo {
	width: 120rpx;
	height: 120rpx;
	border-radius: 8rpx;
	margin-right: 20rpx;
}

.store-name-container {
	flex: 1;
}

.store-name {
	font-size: 36rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 16rpx;
}

.store-tags {
	display: flex;
	flex-wrap: wrap;
}

.tag {
	font-size: 24rpx;
	padding: 6rpx 12rpx;
	border-radius: 4rpx;
	margin-right: 16rpx;
}

.red-tag {
	background-color: #ff4e42;
	color: #ffffff;
}

.light-tag {
	background-color: #fff2f0;
	color: #ff4e42;
}

.favorite-btn {
	color: #999;
}

.store-info-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 24rpx;
}

.distance {
	display: flex;
	align-items: center;
	font-size: 28rpx;
	color: #666;
}

.rating {
	display: flex;
	align-items: center;
}

.stars {
	display: flex;
	align-items: center;
	margin-right: 8rpx;
}

.rating-score {
	display: flex;
	align-items: center;
	font-size: 30rpx;
	color: #ff4e42;
	/* font-weight: bold; */
}

/* 导航选项卡 */
.tabs {
	display: flex;
	background-color: #ffffff;
	border-bottom: 1rpx solid #eee;
	justify-content: flex-start;
	padding-left: 30rpx;
	margin: 0 30rpx;
}

.tab {
	position: relative;
	padding: 30rpx 0;
	margin-right: 60rpx;
	font-size: 30rpx;
	color: #666;
}

.tab.active {
	color: #ff6b35;
	font-weight: 500;
}

.tab-line {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 6rpx;
	background-color: #ff6b35;
	border-radius: 6rpx;
}

/* 促销套餐列表 */
.promotion-list {
	padding: 30rpx;
}

.promotion-item {
	background-color: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 30rpx;
	display: flex;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.food-image {
	width: 180rpx;
	height: 180rpx;
	margin: 20rpx;
}

.food-image image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.promotion-content {
	flex: 1;
	padding: 20rpx;
	position: relative;
}

.rating-tag {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: #ff7000;
	margin-bottom: 12rpx;
}

.meal-title {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 24rpx;
}

.price-container {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.discount-tag {
	position: absolute;
	top: 0;
	left: 0;
	background: linear-gradient(135deg, #ff7000, #ff9e50);
	color: #ffffff;
	font-size: 22rpx;
	padding: 6rpx 12rpx;
	border-radius: 0 0 16rpx 0;
}

.price-info {
	display: flex;
	align-items: center;
}

.current-price {
	font-size: 36rpx;
	color: #ff4e42;
	font-weight: bold;
	margin-right: 12rpx;
}

.original-price {
	font-size: 26rpx;
	color: #999;
	text-decoration: line-through;
	margin-right: 12rpx;
}

.saving {
	font-size: 24rpx;
	color: #ff4e42;
}

.ticket-info {
	font-size: 24rpx;
	color: #ff4e42;
}

.action-btn {
	margin-top: 20rpx;
	text-align: right;
}

.grab-btn {
	display: inline-block;
	background-color: #ff6b35;
	color: #ffffff;
	font-size: 24rpx;
	padding: 2rpx 60rpx;
	border-radius: 30rpx;
	border: none;
	font-weight: normal;
}

/* 优惠券样式 */
.coupon {
	position: relative;
	margin-top: 16rpx;
}

.coupon::before,
.coupon::after {
	content: "";
	position: absolute;
	width: 20rpx;
	height: 20rpx;
	background-color: #f5f5f5;
	border-radius: 50%;
	top: 50%;
	transform: translateY(-50%);
	z-index: 1;
}

.coupon::before {
	left: -10rpx;
}

.coupon::after {
	right: -10rpx;
}

/* 商家信息样式 */
.merchant-info {
	padding: 20rpx 30rpx;
	background-color: #fff;
	margin-top: 30rpx;
	margin-left: 30rpx;
	margin-right: 30rpx;
}

.merchant-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.merchant-item:last-child {
	border-bottom: none;
}

.merchant-item-left {
	display: flex;
	align-items: center;
}

.merchant-text {
	font-size: 30rpx;
	color: #333;
	margin-left: 20rpx;
}

.merchant-item-right {
	display: flex;
	align-items: center;
	color: #666;
	font-size: 28rpx;
}

.business-hours {
	padding: 10rpx 0 30rpx 68rpx;
	font-size: 30rpx;
	color: #333;
	/* border-bottom: 1rpx solid #f5f5f5; */
}

.merchant-services {
	padding: 10rpx 0 30rpx 68rpx;
	display: flex;
	flex-wrap: wrap;
}

.service-item {
	display: flex;
	align-items: center;
	margin-left: 30rpx;
	margin-bottom: 10rpx;
}

.service-item text {
	font-size: 28rpx;
	color: #333;
	margin-left: 8rpx;
}

.empty-tip {
	padding: 100rpx 0;
	text-align: center;
	color: #999;
	font-size: 28rpx;
}

.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 1000;
	background-color: #fff; /* 导航栏背景色 */
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.title {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	font-size: 16px;
	color: #000; /* 标题颜色 */
}

.soldout {
	background: #b6b6b6 !important;
	box-shadow: none;
}
