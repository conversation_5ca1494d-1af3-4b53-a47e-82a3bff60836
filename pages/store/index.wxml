<wxs module="filter" src="../../utils/util.wxs"></wxs>

<!-- 顶部导航栏 -->
<view class="custom-navbar" style="display: none;">
  <!-- 状态栏占位 -->
  <view style="height: {{statusBarHeight}}px;"></view>

  <!-- 导航栏 -->
  <view class="navbar-content" style="height:{{navBarHeight}}px; min-height:{{navBarHeight}}px; line-height:{{navBarHeight}}px;">
    <view class="nav-handle" style="height:{{navBarHeight}}px">
      <view class="back">
      	<t-icon
          prefix="wr"
          name="back"
          size="40rpx"
          style="color:{{sorts === 'asc' ? color : '#bbb'}}"
        />
      </view>
      <view class="home">
        <t-icon
          prefix="wr"
          name="home"
          size="40rpx"
          style="color:{{sorts === 'asc' ? color : '#bbb'}}"
        />
      </view>
    </view>
    <!-- 标题 -->
    <view class="title" style="left: {{titleOffset}}px;">标题</view>
  </view>
</view>


<!-- 店铺详情页 -->
<t-pull-down-refresh
  value="{{enable}}"
  loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}"
  usingCustomNavbar
  bind:refresh="onPullDownRefresh"
>
<view class="store-detail" style="margin-top:0rpx">
  
  <!-- 店铺信息区域 -->
  <view class="store-info">
    <view class="store-card">
      <view class="store-header">
        <image class="store-logo" src="{{shop.logo}}" mode="aspectFill" />
        <view class="store-name-container">
          <view class="store-name">{{shop.name}}</view>
          <view class="store-tags">
            <block wx:for="{{shop.tags}}" wx:key="index">
              <view class="tag {{item.type}}-tag">{{item.text}}</view>
            </block>
          </view>
        </view>
        <!-- 收藏按钮 -->
        <view class="favorite-btn" bindtap="toggleFavoriteAction">
          <t-icon name="{{isShopFavorited ? 'heart-filled' : 'heart'}}" color="{{isShopFavorited ? '#FF4E42' : '#999'}}" size="40rpx" />
        </view>
      </view>
      
      <view class="store-info-footer">
        <view class="distance">
          <t-icon name="location" size="32rpx" />
          <text>距离您{{shop.distance}}</text>
        </view>
        <view class="rating">
          <view class="stars">
            <t-rate value="{{shop.star_rating}}" size="18" gap="2" color="{{['#ffc51c', '#ddd']}}"
              variant="filled" allow-half readonly disabled/>
          </view>
          <text class="rating-score">{{shop.star_rating}}</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 导航选项卡 -->
  <view class="tabs">
    <block wx:for="{{tabs}}" wx:key="index">
      <view class="tab {{activeTab === index ? 'active' : ''}}" bindtap="switchTab" data-index="{{index}}">
        <text>{{item}}</text>
        <view class="tab-line" wx:if="{{activeTab === index}}"></view>
      </view>
    </block>
  </view>
  
  <!-- 商家信息 -->
  <view class="merchant-info" wx:if="{{activeTab === 0}}">
    <!-- 商家地址 -->
    <view class="merchant-item">
      <view class="merchant-item-left" style="width:74%;" >
        <t-icon name="location" color="#FF7000" size="48rpx" />
        <text class="merchant-text">{{shop.address}}</text>
      </view>
      <view class="merchant-item-right" bindtap="navigateToMap" style="width:130rpx;">
        <text>到店吃</text>
        <t-icon name="chevron-right" size="40rpx" color="#999" />
      </view>
    </view>

    <!-- 商家电话 -->
    <view class="merchant-item">
      <view class="merchant-item-left">
        <t-icon name="call" color="#FF7000" size="48rpx" />
        <text class="merchant-text">{{shop.phone}}</text>
      </view>
      <view class="merchant-item-right" bindtap="callMerchant">
        <text>联系商家</text>
        <t-icon name="chevron-right" size="40rpx" color="#999" />
      </view>
    </view>

    <!-- 营业时间 -->
    <view class="merchant-item">
      <view class="merchant-item-left">
        <t-icon name="time" color="#FF7000" size="48rpx" />
        <text class="merchant-text">营业时间</text>
      </view>
      <view class="business-hours">
        <text>{{shop.business_hours}}</text>
      </view>
    </view>
   

    <!-- 商家服务 -->
    <view class="merchant-item">
      <view class="merchant-item-left">
        <t-icon name="thumb-up" color="#FF7000" size="48rpx" />
        <text class="merchant-text">商家服务</text>
      </view>
      <view class="merchant-services">
        <view class="service-item" wx:for="{{storeInfo.services}}" wx:key="index">
          <t-icon name="check-circle-filled" color="#FF7000" size="36rpx" />
          <text>{{item}}</text>
        </view>
      </view>
    </view> 
  </view>

  <!-- 探店活动列表 -->
  <view class="promotion-list" wx:if="{{activeTab === 1}}">
    <block wx:for="{{shop.promotions}}" wx:key="_id">
      <view class="promotion-item" data-promotion-id="{{item._id}}" bind:tap="handleTapProduct">
        <view class="food-image">
          <image src="{{item.product_id.images[0]}}" mode="aspectFill" />
        </view>
        <view class="promotion-content">
          <view class="rating-tag">
            <t-image src="https://636c-cloud1-0gpy573m8caa7db3-**********.tcb.qcloud.la/app/dazhongdianping.svg" width="16" height="16" mode="aspectFill" style="display: flex;margin-right: 10rpx;" wx:if="{{item.platform === 'dianping'}}" />
            <t-image src="https://636c-cloud1-0gpy573m8caa7db3-**********.tcb.qcloud.la/app/xiaohongshu.svg" width="16" height="16" mode="aspectFill" style="display: flex;margin-right: 10rpx" wx:if="{{item.platform === 'xiaohongshu'}}" />
            <text>{{filter.formatPlatform(item.platform)}} {{filter.formatRequirements(item.requires, item.level, item.followers_count)}}</text>
          </view>
          <view class="meal-title">{{item.name}}</view>
          <view class="price-container">
            <view class="price-info">
              <text class="current-price">¥ {{item.current_price}}</text>
              <text class="original-price">¥ {{item.original_price}}</text>
              <text class="saving">省 ¥ {{filter.toFixed(item.original_price - item.current_price)}}</text>
            </view>
            <view class="ticket-info">
              <text>剩余{{item.realtime_quantity}}份</text>
            </view>
          </view>
          <view class="action-btn">
            <button wx:if="{{item.realtime_quantity === 0}}" class="grab-btn soldout" data-id="{{item.id}}" catchtap="handleTapProduct" data-store-id="{{shop.id}}" data-promotion-id="{{item._id}}">已抢完</button>
            <button wx:if="{{item.realtime_quantity > 0}}" class="grab-btn" data-id="{{item.id}}"  catchtap="handleTapProduct" data-store-id="{{shop.id}}" data-promotion-id="{{item._id}}">去抢单</button>
          </view>
        </view>
      </view>
    </block>
    <block wx:if="{{shop.promotions.length === 0}}">
      <view class="no-promotion">
        <text>暂无促销活动</text>
      </view>
    </block>
  </view>

</view>
</t-pull-down-refresh>